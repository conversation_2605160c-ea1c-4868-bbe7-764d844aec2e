import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { EnhancedDashboard } from "@/components/dashboard/enhanced-dashboard";
import { PageLayout } from "@/components/page-layout";
import { auth } from "@/lib/auth";

export default async function DashboardPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  if (!session?.user.onboardingComplete) {
    redirect("/onboarding");
  }

  // Determine if user is a homeowner or professional
  const isProfessional = session?.user?.role === "contractor";

  return (
    <PageLayout title={isProfessional ? "Contractor Dashboard" : "Dashboard"}>
      <EnhancedDashboard />
    </PageLayout>
  );
}
