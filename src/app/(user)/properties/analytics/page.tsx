import { Suspense } from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { PropertyGridSkeleton } from "@/components/loading-states";
import { PageLayout } from "@/components/page-layout";
import { PropertyAnalytics } from "@/components/properties/property-analytics";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";

export default async function PropertyAnalyticsPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Property Analytics">
      <HydrateClient>
        <div className="space-y-6">
          {/* Back Navigation */}
          <div className="flex items-center gap-4">
            <Button asChild variant="ghost" size="sm">
              <Link href="/properties">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Properties
              </Link>
            </Button>
          </div>
          
          <Suspense fallback={<PropertyGridSkeleton />}>
            <PropertyAnalytics />
          </Suspense>
        </div>
      </HydrateClient>
    </PageLayout>
  );
}