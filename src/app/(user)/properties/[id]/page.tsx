import { Suspense } from "react";
import { PageLayout } from "@/components/page-layout";
import { PropertyDetailContent } from "@/components/properties/property-detail-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";

type Props = {
  params: Promise<{ id: string }>;
};

export default async function PropertyDetailPage({ params }: Props) {
  const { id } = await params;
  const queryClient = getQueryClient();

  // Prefetch property and jobs data on the server
  await Promise.all([
    queryClient.prefetchQuery(trpc.properties.getById.queryOptions({ id })),
    queryClient.prefetchQuery(
      trpc.jobs.listByProperty.queryOptions({ propertyId: id }),
    ),
  ]);

  return (
    <PageLayout title="Property Details">
      <HydrateClient>
        <Suspense fallback={<PropertyDetailSkeleton />}>
          <PropertyDetailContent id={id} />
        </Suspense>
      </HydrateClient>
    </PageLayout>
  );
}

function PropertyDetailSkeleton() {
  return (
    <>
      <Skeleton className="mb-8 h-[400px] w-full" />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Skeleton className="h-[200px]" />
        <Skeleton className="h-[200px] md:col-span-2" />
      </div>
    </>
  );
}
