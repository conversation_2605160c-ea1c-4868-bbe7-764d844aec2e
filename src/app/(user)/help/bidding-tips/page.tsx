import { ArrowLeftIcon } from "lucide-react";
import { headers } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";
import { BiddingTipsContent } from "@/components/help/bidding-tips-content";
import { Button } from "@/components/ui/button";
import { auth } from "@/lib/auth";

export { metadata } from "./metadata";

export default async function BiddingTipsPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session) {
    redirect("/sign-in");
  }

  return (
    <div className="container mx-auto space-y-6 p-4 sm:p-6">
      {/* Navigation */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard" className="flex items-center gap-2">
            <ArrowLeftIcon className="h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      {/* Main Content */}
      <BiddingTipsContent />
    </div>
  );
}
