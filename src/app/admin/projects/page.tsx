import { Suspense } from "react";
import { AdminProjectsContent } from "@/components/admin/admin-projects-content";
import { Head<PERSON> } from "@/components/header";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";

export default async function ProjectsPage() {
  const queryClient = getQueryClient();

  // Prefetch projects data on the server
  await queryClient.prefetchQuery(trpc.admin.listJobs.queryOptions());

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <Header title="Projects Management" />
      <HydrateClient>
        <Suspense fallback={<AdminProjectsSkeleton />}>
          <AdminProjectsContent />
        </Suspense>
      </HydrateClient>
    </div>
  );
}

function AdminProjectsSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-64 w-full" />
    </div>
  );
}
