"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Dashboard, useUppyEvent } from "@uppy/react";
import {
  ArrowLeft,
  ArrowRight,
  Building2,
  CheckCircle,
  ChevronsUpDownIcon,
  HomeIcon,
  ImageIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import states from "states-us";
import type { Property } from "@/db/schema";
import { useSession } from "@/lib/auth-client";
import { type PropertyFormData, propertySchema } from "@/lib/schema";
import { createUppy, formatUploadedFileUrl } from "@/lib/uppy";
import { cn } from "@/lib/utils";
import { useTRPC } from "../trpc/client";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Progress } from "../ui/progress";
import { Textarea } from "../ui/textarea";

interface PropertyWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData?: Property & { address?: any };
  onSuccess?: () => void;
}

type WizardStep = "basics" | "location" | "images" | "review";

const PROPERTY_TYPES = [
  { value: "residential", label: "Residential", icon: HomeIcon },
  { value: "commercial", label: "Commercial", icon: Building2 },
  { value: "rental", label: "Rental Property", icon: HomeIcon },
  { value: "vacation", label: "Vacation Home", icon: HomeIcon },
] as const;

export function PropertyWizard({
  open,
  onOpenChange,
  initialData,
  onSuccess,
}: PropertyWizardProps) {
  const [step, setStep] = useState<WizardStep>("basics");
  const [imageUrl, setImageUrl] = useState<string>(initialData?.imageUrl || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [propertyType, setPropertyType] = useState<string>(
    initialData?.type || "",
  );

  const router = useRouter();
  const uppy = useRef(
    createUppy({
      type: "property-image",
      maxFiles: 1,
      allowedFileTypes: ["image/*"],
    }),
  );
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: session } = useSession();
  const user = session?.user;

  const isEditing = !!initialData;

  const createProperty = useMutation(
    trpc.properties.create.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
      },
    }),
  );

  const updateProperty = useMutation(
    trpc.properties.update.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
      },
    }),
  );

  const form = useForm({
    resolver: zodResolver(propertySchema),
    defaultValues: initialData
      ? {
          name: initialData.name || "",
          imageUrl: initialData.imageUrl || "",
          address: {
            street: initialData.address?.street || "",
            city: initialData.address?.city || "",
            state: initialData.address?.state || "",
            zip: initialData.address?.zip || "",
          },
        }
      : {
          name: "",
          imageUrl: "",
          address: {
            street: "",
            city: "",
            state: "",
            zip: "",
          },
        },
  });

  useUppyEvent(uppy.current, "transloadit:result", (stepName, result) => {
    if (stepName === "compressed-image") {
      const url = formatUploadedFileUrl(result.name);
      setImageUrl(url);
      form.setValue("imageUrl", url);
    }
  });

  useEffect(() => {
    if (user) {
      uppy.current.setOptions({ meta: { userId: user.id } });
    }
  }, [user]);

  const getStepProgress = () => {
    const steps = ["basics", "location", "images", "review"];
    return ((steps.indexOf(step) + 1) / steps.length) * 100;
  };

  const getStepTitle = () => {
    switch (step) {
      case "basics":
        return "Property Basics";
      case "location":
        return "Location Details";
      case "images":
        return "Property Images";
      case "review":
        return "Review & Create";
      default:
        return "";
    }
  };

  const getStepDescription = () => {
    switch (step) {
      case "basics":
        return "Tell us about your property";
      case "location":
        return "Where is your property located?";
      case "images":
        return "Add photos of your property";
      case "review":
        return "Review your information before creating";
      default:
        return "";
    }
  };

  const canProceedToNext = () => {
    switch (step) {
      case "basics":
        return form.watch("name")?.trim().length > 0 && propertyType;
      case "location": {
        const address = form.watch("address");
        return (
          address?.street?.trim() &&
          address?.city?.trim() &&
          address?.state?.trim() &&
          address?.zip?.trim()
        );
      }
      case "images":
        return true; // Images are optional
      case "review":
        return true;
      default:
        return false;
    }
  };

  const handleNext = async () => {
    let isValid = true;

    // Validate current step
    switch (step) {
      case "basics":
        isValid = await form.trigger(["name"]);
        break;
      case "location":
        isValid = await form.trigger([
          "address.street",
          "address.city",
          "address.state",
          "address.zip",
        ]);
        break;
      case "images":
        isValid = true; // Images are optional
        break;
    }

    if (!isValid) return;

    // Move to next step
    switch (step) {
      case "basics":
        setStep("location");
        break;
      case "location":
        setStep("images");
        break;
      case "images":
        setStep("review");
        break;
    }
  };

  const handleBack = () => {
    switch (step) {
      case "location":
        setStep("basics");
        break;
      case "images":
        setStep("location");
        break;
      case "review":
        setStep("images");
        break;
    }
  };

  const handleClose = () => {
    setStep("basics");
    setPropertyType("");
    setImageUrl("");
    form.reset();
    onOpenChange(false);
  };

  const onSubmit: SubmitHandler<PropertyFormData> = async (data) => {
    try {
      setIsSubmitting(true);

      const propertyData = {
        ...data,
        imageUrl: imageUrl || undefined,
        type: propertyType,
      };

      if (isEditing && initialData) {
        await updateProperty.mutateAsync({
          id: initialData.id,
          ...propertyData,
        });
        toast.success("Success", {
          description: "Property updated successfully!",
        });
      } else {
        const result = await createProperty.mutateAsync(propertyData);
        toast.success("Success", {
          description: "Property created successfully!",
        });
        router.push(`/properties/${result.id}`);
      }

      onSuccess?.();
      handleClose();
    } catch (error) {
      console.error("Error submitting property:", error);
      toast.error("Error", {
        description: `Failed to ${isEditing ? "update" : "create"} property. Please try again.`,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-hidden">
        <DialogHeader>
          <div className="space-y-4">
            <div>
              <DialogTitle>{getStepTitle()}</DialogTitle>
              <DialogDescription>{getStepDescription()}</DialogDescription>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-muted-foreground text-sm">
                <span>
                  Step{" "}
                  {["basics", "location", "images", "review"].indexOf(step) + 1}{" "}
                  of 4
                </span>
                <span>{Math.round(getStepProgress())}% complete</span>
              </div>
              <Progress value={getStepProgress()} className="h-2" />
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-1">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Step 1: Property Basics */}
              {step === "basics" && (
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Property Name *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="e.g. Main Street House, Downtown Office"
                            className="focus-visible:ring-orange-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-3">
                    <FormLabel>Property Type *</FormLabel>
                    <div className="grid grid-cols-2 gap-3">
                      {PROPERTY_TYPES.map((type) => {
                        const Icon = type.icon;
                        return (
                          <button
                            key={type.value}
                            type="button"
                            className={cn(
                              "rounded-lg border p-4 text-left transition-all hover:border-orange-500",
                              propertyType === type.value
                                ? "border-orange-500 bg-orange-50"
                                : "border-gray-200",
                            )}
                            onClick={() => setPropertyType(type.value)}
                          >
                            <div className="flex items-center gap-3">
                              <Icon className="h-5 w-5 text-orange-500" />
                              <span className="font-medium">{type.label}</span>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Brief description of the property..."
                            className="min-h-[80px] focus-visible:ring-orange-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Step 2: Location Details */}
              {step === "location" && (
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="address.street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            autoComplete="address-line1"
                            placeholder="123 Main Street"
                            className="focus-visible:ring-orange-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="address.city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City *</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              autoComplete="address-level2"
                              placeholder="San Francisco"
                              className="focus-visible:ring-orange-500"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.zip"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ZIP Code *</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              autoComplete="postal-code"
                              placeholder="94103"
                              className="focus-visible:ring-orange-500"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? states.find(
                                      (state) =>
                                        state.abbreviation === field.value,
                                    )?.name
                                  : "Select state"}
                                <ChevronsUpDownIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search state..."
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>No state found.</CommandEmpty>
                                <CommandGroup>
                                  {states.map((state) => (
                                    <CommandItem
                                      value={state.abbreviation}
                                      key={state.abbreviation}
                                      onSelect={(value) => {
                                        form.setValue("address.state", value);
                                      }}
                                    >
                                      {state.name}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Step 3: Property Images */}
              {step === "images" && (
                <div className="space-y-6">
                  <div className="text-center">
                    <ImageIcon className="mx-auto mb-4 h-12 w-12 text-orange-500" />
                    <h3 className="mb-2 font-medium text-lg">
                      Add Property Photos
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      Upload a main photo of your property. This helps
                      contractors understand your project better.
                    </p>
                  </div>

                  <div className="rounded-lg bg-muted/30 p-4">
                    <Dashboard
                      uppy={uppy.current}
                      height={250}
                      width="100%"
                      theme="auto"
                      proudlyDisplayPoweredByUppy={false}
                      showLinkToFileUploadResult={false}
                      showProgressDetails
                      note="Images should be in JPG or PNG format, max 10MB"
                    />
                    {imageUrl && (
                      <div className="mt-4 flex items-center gap-2 text-green-600 text-sm">
                        <CheckCircle className="h-4 w-4" />
                        Image uploaded successfully
                      </div>
                    )}
                  </div>

                  <div className="text-center text-muted-foreground text-sm">
                    Don't have photos ready? You can skip this step and add them
                    later.
                  </div>
                </div>
              )}

              {/* Step 4: Review & Create */}
              {step === "review" && (
                <div className="space-y-6">
                  <div className="text-center">
                    <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                    <h3 className="mb-2 font-medium text-lg">
                      Review Your Property
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      Please review the information below before creating your
                      property.
                    </p>
                  </div>

                  <Card>
                    <CardContent className="space-y-4 p-6">
                      <div>
                        <h4 className="mb-1 font-medium text-muted-foreground text-sm">
                          PROPERTY NAME
                        </h4>
                        <p className="font-medium">{form.watch("name")}</p>
                      </div>

                      {propertyType && (
                        <div>
                          <h4 className="mb-1 font-medium text-muted-foreground text-sm">
                            TYPE
                          </h4>
                          <Badge variant="secondary">
                            {
                              PROPERTY_TYPES.find(
                                (t) => t.value === propertyType,
                              )?.label
                            }
                          </Badge>
                        </div>
                      )}

                      <div>
                        <h4 className="mb-1 font-medium text-muted-foreground text-sm">
                          ADDRESS
                        </h4>
                        <p>
                          {form.watch("address.street")}
                          <br />
                          {form.watch("address.city")},{" "}
                          {form.watch("address.state")}{" "}
                          {form.watch("address.zip")}
                        </p>
                      </div>

                      {form.watch("description") && (
                        <div>
                          <h4 className="mb-1 font-medium text-muted-foreground text-sm">
                            DESCRIPTION
                          </h4>
                          <p className="text-sm">{form.watch("description")}</p>
                        </div>
                      )}

                      {imageUrl && (
                        <div>
                          <h4 className="mb-1 font-medium text-muted-foreground text-sm">
                            IMAGE
                          </h4>
                          <div className="flex items-center gap-2 text-green-600 text-sm">
                            <CheckCircle className="h-4 w-4" />
                            Property image uploaded
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </form>
          </Form>
        </div>

        {/* Footer with navigation */}
        <div className="flex items-center justify-between border-t pt-4">
          <div>
            {step !== "basics" && (
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>

            {step !== "review" ? (
              <Button
                onClick={handleNext}
                disabled={!canProceedToNext()}
                className="bg-orange-600 hover:bg-orange-700"
              >
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isSubmitting
                  ? isEditing
                    ? "Updating..."
                    : "Creating..."
                  : isEditing
                    ? "Update Property"
                    : "Create Property"}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
