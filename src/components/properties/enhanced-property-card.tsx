import {
  Activity,
  Building2,
  Calendar,
  Edit,
  Eye,
  MapPin,
  Plus,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { JobWizard } from "@/components/job/job-wizard";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import type { Property } from "@/db/schema";

interface EnhancedPropertyCardProps {
  property: Property & {
    address?: {
      street: string;
      city: string;
      state: string;
      zip: string;
    } | null;
    _count?: {
      jobs: number;
    };
    recentActivity?: {
      lastJobDate?: string;
      activeJobs?: number;
    };
  };
  showStats?: boolean;
  compact?: boolean;
}

export function EnhancedPropertyCard({
  property,
  showStats = true,
  compact = false,
}: EnhancedPropertyCardProps) {
  const [wizardO<PERSON>, setWizardOpen] = useState(false);
  const [imageError, setImageError] = useState(false);

  const formatAddress = (address: typeof property.address) => {
    if (!address) return "No address provided";
    return `${address.street}, ${address.city}, ${address.state} ${address.zip}`;
  };

  const getActivityStatus = () => {
    const activeJobs = property.recentActivity?.activeJobs || 0;
    if (activeJobs > 0) {
      return {
        label: `${activeJobs} Active`,
        variant: "default" as const,
        color: "text-green-600",
      };
    }
    return {
      label: "Inactive",
      variant: "secondary" as const,
      color: "text-muted-foreground",
    };
  };

  if (compact) {
    return (
      <>
        <Card className="group transition-all duration-200 hover:scale-[1.02] hover:shadow-md">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {/* Property Image */}
              <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-lg">
                {property.imageUrl && !imageError ? (
                  <Image
                    src={property.imageUrl}
                    alt={property.name}
                    fill
                    className="object-cover"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-muted">
                    <Building2 className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
              </div>

              {/* Property Info */}
              <div className="min-w-0 flex-1">
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    <h3 className="truncate font-semibold text-sm">
                      {property.name}
                    </h3>
                    <p className="truncate text-muted-foreground text-xs">
                      {property.address
                        ? `${property.address.city}, ${property.address.state}`
                        : "No address"}
                    </p>
                  </div>
                  {showStats && (
                    <Badge
                      variant={getActivityStatus().variant}
                      className="ml-2 text-xs"
                    >
                      {getActivityStatus().label}
                    </Badge>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="mt-3 flex gap-2">
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    className="h-8 px-3 text-xs"
                  >
                    <Link href={`/properties/${property.id}`}>
                      <Eye className="mr-1 h-3 w-3" />
                      View
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    variant="default"
                    className="h-8 bg-tradecrews-orange-500 px-3 text-xs hover:bg-tradecrews-orange-600"
                    onClick={() => setWizardOpen(true)}
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Project
                  </Button>
                  <Button
                    asChild
                    size="sm"
                    variant="ghost"
                    className="h-8 px-3 text-xs"
                  >
                    <Link href={`/properties/${property.id}/edit`}>
                      <Edit className="h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <JobWizard
          open={wizardOpen}
          onOpenChange={setWizardOpen}
          propertyId={property.id}
        />
      </>
    );
  }

  return (
    <>
      <Card className="group w-full overflow-hidden transition-all duration-200 hover:scale-[1.02] hover:shadow-lg">
        {/* Property Image */}
        <div className="relative h-48 overflow-hidden">
          {property.imageUrl && !imageError ? (
            <Image
              src={property.imageUrl}
              alt={property.name}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-muted to-muted/50">
              <Building2 className="h-16 w-16 text-muted-foreground" />
            </div>
          )}

          {/* Activity Badge Overlay */}
          {showStats && (
            <div className="absolute top-3 right-3">
              <Badge
                variant={getActivityStatus().variant}
                className="shadow-sm"
              >
                <Activity className="mr-1 h-3 w-3" />
                {getActivityStatus().label}
              </Badge>
            </div>
          )}
        </div>

        <CardContent className="space-y-3 p-4">
          {/* Property Name */}
          <div>
            <h3 className="font-semibold text-lg leading-tight">
              {property.name}
            </h3>
          </div>

          {/* Address */}
          <div className="flex items-start gap-2">
            <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0 text-muted-foreground" />
            <p className="text-muted-foreground text-sm leading-relaxed">
              {formatAddress(property.address)}
            </p>
          </div>

          {/* Stats */}
          {showStats && (
            <>
              <Separator />
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-tradecrews-orange-500" />
                  <div>
                    <p className="font-medium">{property._count?.jobs || 0}</p>
                    <p className="text-muted-foreground text-xs">Projects</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="font-medium">
                      {property.recentActivity?.activeJobs || 0}
                    </p>
                    <p className="text-muted-foreground text-xs">Active</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>

        <CardFooter className="flex flex-col gap-3 p-4 pt-0">
          {/* Primary Actions */}
          <div className="grid w-full grid-cols-2 gap-3">
            <Button asChild variant="outline" className="h-9">
              <Link href={`/properties/${property.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View
              </Link>
            </Button>
            <Button
              variant="default"
              className="h-9 bg-tradecrews-orange-500 hover:bg-tradecrews-orange-600"
              onClick={() => setWizardOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Project
            </Button>
          </div>

          {/* Secondary Action */}
          <Button asChild variant="ghost" className="h-9 w-full">
            <Link href={`/properties/${property.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Property
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <JobWizard
        open={wizardOpen}
        onOpenChange={setWizardOpen}
        propertyId={property.id}
      />
    </>
  );
}
