import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: number | string;
  description: string;
}

export function StatsCard({ title, value, description }: StatsCardProps) {
  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-1 sm:pb-2">
        <CardDescription className="text-xs sm:text-sm">
          {title}
        </CardDescription>
        <CardTitle className="text-xl sm:text-2xl lg:text-3xl">
          {value}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-muted-foreground text-xs leading-tight sm:leading-normal">
          {description}
        </p>
      </CardContent>
    </Card>
  );
}
