"use client";

import { useQuery } from "@tanstack/react-query";
import { StatsCard } from "@/components/dashboard/stats-card";
import { useTRPC } from "@/components/trpc/client";

interface StatsOverviewProps {
  isProfessional: boolean;
}

export function StatsOverview({ isProfessional }: StatsOverviewProps) {
  const trpc = useTRPC();

  // Homeowner data
  const { data: homeownerStats } = useQuery(
    trpc.accounts.getStats.queryOptions(undefined, {
      enabled: !isProfessional,
    }),
  );

  // Professional data
  const { data: contractorStats } = useQuery(
    trpc.contractor.getStats.queryOptions(undefined, {
      enabled: isProfessional,
    }),
  );

  // Stats calculations...
  const totalJobs = isProfessional
    ? contractorStats?.totalBids || 0
    : homeownerStats?.totalJobs || 0;
  const activeJobs = isProfessional
    ? contractorStats?.activeJobs || 0
    : homeownerStats?.activeJobs || 0;
  const completedJobs = isProfessional
    ? contractorStats?.completedJobs || 0
    : homeownerStats?.completedJobs || 0;

  return (
    <div className="grid grid-cols-3 gap-3 sm:gap-4 md:grid-cols-3">
      <StatsCard
        title={isProfessional ? "Total Bids" : "Total Jobs"}
        value={totalJobs}
        description={
          isProfessional
            ? "All bids you've submitted"
            : "Across all your properties"
        }
      />

      <StatsCard
        title="Active Jobs"
        value={activeJobs}
        description={
          isProfessional
            ? "Jobs you're currently working on"
            : "Currently published and accepting bids"
        }
      />

      <StatsCard
        title="Completed Jobs"
        value={completedJobs}
        description={
          isProfessional
            ? "Successfully completed projects"
            : "Successfully awarded or closed"
        }
      />
    </div>
  );
}
