"use client";

import {
  AlertTriangleIcon,
  BellIcon,
  CheckCircleIcon,
  InfoIcon,
  XIcon,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface SmartAlert {
  id: string;
  type: "info" | "warning" | "success" | "error";
  title: string;
  message: string;
  action?: {
    label: string;
    href: string;
  };
  dismissible?: boolean;
  priority: "high" | "medium" | "low";
  timestamp?: Date;
}

interface SmartAlertsProps {
  alerts: SmartAlert[];
  onDismiss?: (alertId: string) => void;
  className?: string;
}

export function SmartAlerts({
  alerts,
  onDismiss,
  className,
}: SmartAlertsProps) {
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(
    new Set(),
  );

  const handleDismiss = (alertId: string) => {
    setDismissedAlerts((prev) => new Set([...prev, alertId]));
    onDismiss?.(alertId);
  };

  const visibleAlerts = alerts
    .filter((alert) => !dismissedAlerts.has(alert.id))
    .sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

  if (visibleAlerts.length === 0) return null;

  const getAlertIcon = (type: SmartAlert["type"]) => {
    switch (type) {
      case "info":
        return <InfoIcon className="h-4 w-4" />;
      case "warning":
        return <AlertTriangleIcon className="h-4 w-4" />;
      case "success":
        return <CheckCircleIcon className="h-4 w-4" />;
      case "error":
        return <AlertTriangleIcon className="h-4 w-4" />;
      default:
        return <BellIcon className="h-4 w-4" />;
    }
  };

  const getAlertStyles = (
    type: SmartAlert["type"],
    priority: SmartAlert["priority"],
  ) => {
    const baseStyles = "border-l-4 transition-all duration-200";

    const typeStyles = {
      info: "border-l-blue-500 bg-blue-50 dark:bg-blue-950/20",
      warning: "border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950/20",
      success: "border-l-green-500 bg-green-50 dark:bg-green-950/20",
      error: "border-l-red-500 bg-red-50 dark:bg-red-950/20",
    };

    const priorityStyles = {
      high: "ring-2 ring-offset-2 ring-orange-500/20",
      medium: "",
      low: "opacity-90",
    };

    return cn(baseStyles, typeStyles[type], priorityStyles[priority]);
  };

  const getTextStyles = (type: SmartAlert["type"]) => {
    const styles = {
      info: "text-blue-800 dark:text-blue-200",
      warning: "text-yellow-800 dark:text-yellow-200",
      success: "text-green-800 dark:text-green-200",
      error: "text-red-800 dark:text-red-200",
    };
    return styles[type];
  };

  return (
    <Card className={cn("shadow-sm", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <BellIcon className="h-5 w-5 text-orange-500" />
          Notifications
          {visibleAlerts.length > 0 && (
            <span className="rounded-full bg-orange-500 px-2 py-0.5 text-white text-xs">
              {visibleAlerts.length}
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {visibleAlerts.map((alert) => (
          <div
            key={alert.id}
            className={cn(
              "rounded-lg p-3",
              getAlertStyles(alert.type, alert.priority),
            )}
          >
            <div className="flex items-start gap-3">
              <div
                className={cn(
                  "mt-0.5 flex-shrink-0",
                  getTextStyles(alert.type),
                )}
              >
                {getAlertIcon(alert.type)}
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <h4
                      className={cn(
                        "font-medium text-sm",
                        getTextStyles(alert.type),
                      )}
                    >
                      {alert.title}
                    </h4>
                    <p
                      className={cn(
                        "mt-1 text-sm opacity-90",
                        getTextStyles(alert.type),
                      )}
                    >
                      {alert.message}
                    </p>
                    {alert.timestamp && (
                      <p
                        className={cn(
                          "mt-2 text-xs opacity-75",
                          getTextStyles(alert.type),
                        )}
                      >
                        {alert.timestamp.toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                  {alert.dismissible && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-black/10"
                      onClick={() => handleDismiss(alert.id)}
                    >
                      <XIcon className="h-3 w-3" />
                    </Button>
                  )}
                </div>
                {alert.action && (
                  <div className="mt-3">
                    <Button
                      size="sm"
                      variant={
                        alert.type === "error" ? "destructive" : "default"
                      }
                      className="text-xs"
                      asChild
                    >
                      <Link href={alert.action.href}>{alert.action.label}</Link>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

// Helper functions to generate smart alerts
export function generateHomeownerAlerts(data: {
  pendingBids: number;
  expiringSoon: number;
  completedJobs: number;
  unreadMessages: number;
}): SmartAlert[] {
  const alerts: SmartAlert[] = [];

  if (data.pendingBids > 0) {
    alerts.push({
      id: "pending-bids",
      type: "warning",
      title: "Bids Awaiting Review",
      message: `You have ${data.pendingBids} bid${data.pendingBids > 1 ? "s" : ""} waiting for your review.`,
      action: {
        label: "Review Bids",
        href: "/bids",
      },
      priority: "high",
      dismissible: true,
      timestamp: new Date(),
    });
  }

  if (data.expiringSoon > 0) {
    alerts.push({
      id: "expiring-projects",
      type: "warning",
      title: "Projects Expiring Soon",
      message: `${data.expiringSoon} project${data.expiringSoon > 1 ? "s" : ""} will close for bidding soon.`,
      action: {
        label: "Extend Deadline",
        href: "/projects",
      },
      priority: "medium",
      dismissible: true,
    });
  }

  if (data.unreadMessages > 0) {
    alerts.push({
      id: "unread-messages",
      type: "info",
      title: "New Messages",
      message: `You have ${data.unreadMessages} unread message${data.unreadMessages > 1 ? "s" : ""} from contractors.`,
      action: {
        label: "View Messages",
        href: "/messages",
      },
      priority: "medium",
      dismissible: true,
    });
  }

  return alerts;
}

export function generateContractorAlerts(data: {
  newJobs: number;
  bidDeadlines: number;
  acceptedBids: number;
  unreadMessages: number;
}): SmartAlert[] {
  const alerts: SmartAlert[] = [];

  if (data.newJobs > 0) {
    alerts.push({
      id: "new-jobs",
      type: "success",
      title: "New Jobs Available",
      message: `${data.newJobs} new job${data.newJobs > 1 ? "s" : ""} match your skills and location.`,
      action: {
        label: "View Jobs",
        href: "/projects",
      },
      priority: "high",
      dismissible: true,
      timestamp: new Date(),
    });
  }

  if (data.bidDeadlines > 0) {
    alerts.push({
      id: "bid-deadlines",
      type: "warning",
      title: "Bid Deadlines Approaching",
      message: `${data.bidDeadlines} job${data.bidDeadlines > 1 ? "s" : ""} you're interested in close for bidding soon.`,
      action: {
        label: "Submit Bids",
        href: "/projects",
      },
      priority: "high",
      dismissible: true,
    });
  }

  if (data.acceptedBids > 0) {
    alerts.push({
      id: "accepted-bids",
      type: "success",
      title: "Congratulations!",
      message: `${data.acceptedBids} of your bid${data.acceptedBids > 1 ? "s have" : " has"} been accepted.`,
      action: {
        label: "View Projects",
        href: "/projects",
      },
      priority: "high",
      dismissible: true,
    });
  }

  return alerts;
}
