"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>gleIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircleIcon,
  ClockIcon,
  TrendingUpIcon,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface ActionableStat {
  id: string;
  title: string;
  value: number | string;
  change?: {
    value: number;
    type: "increase" | "decrease";
    period: string;
  };
  progress?: {
    current: number;
    total: number;
    label: string;
  };
  action?: {
    label: string;
    href: string;
    variant?: "default" | "outline" | "secondary";
  };
  status?: "good" | "warning" | "critical";
  description: string;
  icon: React.ReactNode;
}

interface ActionableStatsProps {
  stats: ActionableStat[];
  className?: string;
}

export function ActionableStats({ stats, className }: ActionableStatsProps) {
  return (
    <div
      className={cn(
        "grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",
        className,
      )}
    >
      {stats.map((stat) => (
        <Card key={stat.id} className="shadow-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "rounded-lg p-2",
                    stat.status === "good" &&
                      "bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-400",
                    stat.status === "warning" &&
                      "bg-yellow-100 text-yellow-600 dark:bg-yellow-950 dark:text-yellow-400",
                    stat.status === "critical" &&
                      "bg-red-100 text-red-600 dark:bg-red-950 dark:text-red-400",
                    !stat.status && "bg-muted text-muted-foreground",
                  )}
                >
                  {stat.icon}
                </div>
                <div>
                  <CardTitle className="font-medium text-sm">
                    {stat.title}
                  </CardTitle>
                  <div className="font-bold text-2xl">{stat.value}</div>
                </div>
              </div>
              {stat.change && (
                <div
                  className={cn(
                    "flex items-center gap-1 text-sm",
                    stat.change.type === "increase"
                      ? "text-green-600 dark:text-green-400"
                      : "text-red-600 dark:text-red-400",
                  )}
                >
                  {stat.change.type === "increase" ? (
                    <ArrowUpIcon className="h-3 w-3" />
                  ) : (
                    <ArrowDownIcon className="h-3 w-3" />
                  )}
                  <span>{Math.abs(stat.change.value)}%</span>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-muted-foreground text-sm">{stat.description}</p>

            {stat.progress && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{stat.progress.label}</span>
                  <span>
                    {stat.progress.current}/{stat.progress.total}
                  </span>
                </div>
                <Progress
                  value={(stat.progress.current / stat.progress.total) * 100}
                  className="h-2"
                />
              </div>
            )}

            {stat.change && (
              <p className="text-muted-foreground text-xs">
                {stat.change.type === "increase" ? "↗" : "↘"}{" "}
                {stat.change.period}
              </p>
            )}

            {stat.action && (
              <Button
                variant={stat.action.variant || "outline"}
                size="sm"
                className="w-full"
                asChild
              >
                <Link href={stat.action.href}>{stat.action.label}</Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Helper function to generate stats for homeowners
export function generateHomeownerStats(data: {
  totalJobs: number;
  activeJobs: number;
  completedJobs: number;
  pendingBids: number;
  avgResponseTime: number;
}): ActionableStat[] {
  return [
    {
      id: "active-projects",
      title: "Active Projects",
      value: data.activeJobs,
      description: "Projects currently accepting bids or in progress",
      icon: <ClockIcon className="h-4 w-4" />,
      status: data.activeJobs > 0 ? "good" : "warning",
      action:
        data.activeJobs === 0
          ? {
              label: "Start New Project",
              href: "/projects/new",
              variant: "default",
            }
          : {
              label: "View Projects",
              href: "/projects",
            },
    },
    {
      id: "pending-bids",
      title: "Pending Bids",
      value: data.pendingBids,
      description: "Bids waiting for your review",
      icon: <AlertTriangleIcon className="h-4 w-4" />,
      status: data.pendingBids > 0 ? "warning" : "good",
      action:
        data.pendingBids > 0
          ? {
              label: "Review Bids",
              href: "/bids",
              variant: "default",
            }
          : undefined,
    },
    {
      id: "completion-rate",
      title: "Completion Rate",
      value:
        data.totalJobs > 0
          ? `${Math.round((data.completedJobs / data.totalJobs) * 100)}%`
          : "0%",
      description: "Successfully completed projects",
      icon: <CheckCircleIcon className="h-4 w-4" />,
      progress: {
        current: data.completedJobs,
        total: data.totalJobs,
        label: "Completed",
      },
      status:
        data.totalJobs > 0 && data.completedJobs / data.totalJobs > 0.8
          ? "good"
          : "warning",
    },
  ];
}

// Helper function to generate stats for contractors
export function generateContractorStats(data: {
  totalBids: number;
  activeJobs: number;
  completedJobs: number;
  winRate: number;
  avgResponseTime: number;
}): ActionableStat[] {
  return [
    {
      id: "active-jobs",
      title: "Active Jobs",
      value: data.activeJobs,
      description: "Jobs you're currently working on",
      icon: <ClockIcon className="h-4 w-4" />,
      status: data.activeJobs > 0 ? "good" : "warning",
      action:
        data.activeJobs === 0
          ? {
              label: "Find Jobs",
              href: "/projects",
              variant: "default",
            }
          : {
              label: "View Jobs",
              href: "/projects",
            },
    },
    {
      id: "win-rate",
      title: "Win Rate",
      value: `${data.winRate}%`,
      description: "Percentage of bids that get accepted",
      icon: <TrendingUpIcon className="h-4 w-4" />,
      status:
        data.winRate > 30 ? "good" : data.winRate > 15 ? "warning" : "critical",
      progress: {
        current: data.winRate,
        total: 100,
        label: "Win Rate",
      },
      action:
        data.winRate < 20
          ? {
              label: "Improve Bids",
              href: "/help/bidding-tips",
            }
          : undefined,
    },
    {
      id: "response-time",
      title: "Response Time",
      value: `${data.avgResponseTime}h`,
      description: "Average time to respond to job posts",
      icon: <ClockIcon className="h-4 w-4" />,
      status:
        data.avgResponseTime < 24
          ? "good"
          : data.avgResponseTime < 48
            ? "warning"
            : "critical",
      action:
        data.avgResponseTime > 24
          ? {
              label: "Enable Notifications",
              href: "/profile/notifications",
            }
          : undefined,
    },
  ];
}
