"use client";

import {
  CalendarIcon,
  MessageSquareIcon,
  PlusCircleIcon,
  SearchIcon,
  SettingsIcon,
  UserIcon,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface QuickAction {
  id: string;
  label: string;
  href: string;
  icon: React.ReactNode;
  description: string;
  variant?: "default" | "outline" | "secondary";
  badge?: string;
}

interface QuickActionsProps {
  actions: QuickAction[];
  title: string;
  layout?: "grid" | "list";
  className?: string;
}

export function QuickActions({
  actions,
  title,
  layout = "grid",
  className,
}: QuickActionsProps) {
  return (
    <Card className={cn("shadow-sm", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <Card<PERSON>ontent>
        <div
          className={cn(
            layout === "grid"
              ? "grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-4"
              : "space-y-2",
          )}
        >
          {actions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant || "outline"}
              className={cn(
                "h-auto flex-col gap-2 p-4 text-center",
                layout === "list" &&
                  "h-12 flex-row justify-start p-3 text-left",
              )}
              asChild
            >
              <Link href={action.href}>
                <div
                  className={cn(
                    "flex items-center gap-2",
                    layout === "grid" && "flex-col",
                  )}
                >
                  <div className="relative">
                    {action.icon}
                    {action.badge && (
                      <span className="-top-1 -right-1 absolute flex h-3 w-3 items-center justify-center rounded-full bg-red-500 text-white text-xs">
                        {action.badge}
                      </span>
                    )}
                  </div>
                  <div className={cn(layout === "grid" && "text-center")}>
                    <div className="font-medium text-sm">{action.label}</div>
                    {layout === "grid" && (
                      <div className="mt-1 text-muted-foreground text-xs">
                        {action.description}
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// Predefined action sets for different user types
export const homeownerQuickActions: QuickAction[] = [
  {
    id: "new-project",
    label: "New Project",
    href: "/projects/new",
    icon: <PlusCircleIcon className="h-5 w-5" />,
    description: "Start a new project",
    variant: "default",
  },
  {
    id: "browse-contractors",
    label: "Find Contractors",
    href: "/contractors",
    icon: <SearchIcon className="h-5 w-5" />,
    description: "Browse professionals",
  },
  {
    id: "calendar",
    label: "Calendar",
    href: "/calendar",
    icon: <CalendarIcon className="h-5 w-5" />,
    description: "View schedule",
  },
  {
    id: "messages",
    label: "Messages",
    href: "/messages",
    icon: <MessageSquareIcon className="h-5 w-5" />,
    description: "Chat with contractors",
  },
  {
    id: "properties",
    label: "Properties",
    href: "/properties",
    icon: <UserIcon className="h-5 w-5" />,
    description: "Manage properties",
  },
  {
    id: "settings",
    label: "Settings",
    href: "/profile",
    icon: <SettingsIcon className="h-5 w-5" />,
    description: "Account settings",
  },
];

export const contractorQuickActions: QuickAction[] = [
  {
    id: "browse-jobs",
    label: "Browse Jobs",
    href: "/projects",
    icon: <SearchIcon className="h-5 w-5" />,
    description: "Find new projects",
    variant: "default",
  },
  {
    id: "calendar",
    label: "Calendar",
    href: "/calendar",
    icon: <CalendarIcon className="h-5 w-5" />,
    description: "View schedule",
  },
  {
    id: "messages",
    label: "Messages",
    href: "/messages",
    icon: <MessageSquareIcon className="h-5 w-5" />,
    description: "Chat with clients",
  },
  {
    id: "organization",
    label: "My Company",
    href: "/contractors/settings",
    icon: <UserIcon className="h-5 w-5" />,
    description: "Company profile",
  },
  {
    id: "settings",
    label: "Settings",
    href: "/profile",
    icon: <SettingsIcon className="h-5 w-5" />,
    description: "Account settings",
  },
];
