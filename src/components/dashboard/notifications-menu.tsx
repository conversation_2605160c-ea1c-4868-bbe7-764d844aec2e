"use client";

import { 
  BellIcon, 
  CheckIcon,
  XIcon,
  AlertTriangleIcon,
  InfoIcon,
  CheckCircleIcon
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface Notification {
  id: string;
  type: "info" | "warning" | "success" | "error";
  title: string;
  message: string;
  action?: {
    label: string;
    href: string;
  };
  timestamp: Date;
  read?: boolean;
}

interface NotificationsMenuProps {
  notifications: Notification[];
  onMarkAsRead?: (notificationId: string) => void;
  onMarkAllAsRead?: () => void;
  onDismiss?: (notificationId: string) => void;
  className?: string;
}

export function NotificationsMenu({ 
  notifications, 
  onMarkAsRead, 
  onMarkAllAsRead,
  onDismiss,
  className 
}: NotificationsMenuProps) {
  const [open, setOpen] = useState(false);
  
  const unreadCount = notifications.filter(n => !n.read).length;
  const sortedNotifications = notifications
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10); // Show max 10 notifications

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "info":
        return <InfoIcon className="h-4 w-4 text-blue-500" />;
      case "warning":
        return <AlertTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case "success":
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertTriangleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <BellIcon className="h-4 w-4" />;
    }
  };

  const formatTime = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn("relative", className)}
        >
          <BellIcon className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="end" 
        className="w-80 sm:w-96"
        sideOffset={8}
      >
        <div className="flex items-center justify-between px-4 py-2">
          <DropdownMenuLabel className="p-0">
            Notifications
            {unreadCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {unreadCount} new
              </Badge>
            )}
          </DropdownMenuLabel>
          {unreadCount > 0 && onMarkAllAsRead && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMarkAllAsRead}
              className="h-auto p-1 text-xs"
            >
              <CheckIcon className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </div>
        <DropdownMenuSeparator />
        
        {sortedNotifications.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground text-sm">
            No notifications
          </div>
        ) : (
          <ScrollArea className="max-h-96">
            {sortedNotifications.map((notification, index) => (
              <div key={notification.id}>
                <div
                  className={cn(
                    "p-3 hover:bg-muted/50 cursor-pointer transition-colors",
                    !notification.read && "bg-blue-50 dark:bg-blue-950/20"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <p className={cn(
                            "font-medium text-sm",
                            !notification.read && "font-semibold"
                          )}>
                            {notification.title}
                          </p>
                          <p className="text-muted-foreground text-xs mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          <p className="text-muted-foreground text-xs mt-2">
                            {formatTime(notification.timestamp)}
                          </p>
                        </div>
                        {onDismiss && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-950"
                            onClick={(e) => {
                              e.stopPropagation();
                              onDismiss(notification.id);
                            }}
                          >
                            <XIcon className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                      {notification.action && (
                        <div className="mt-2">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 text-xs"
                            asChild
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Link href={notification.action.href}>
                              {notification.action.label}
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {index < sortedNotifications.length - 1 && (
                  <DropdownMenuSeparator />
                )}
              </div>
            ))}
          </ScrollArea>
        )}
        
        {notifications.length > 10 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/notifications" className="w-full text-center">
                View all notifications
              </Link>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Helper function to generate notifications from alerts
export function alertsToNotifications(alerts: Array<{
  id: string;
  type: "info" | "warning" | "success" | "error";
  title: string;
  message: string;
  action?: { label: string; href: string; };
  timestamp?: Date;
}>): Notification[] {
  return alerts.map(alert => ({
    ...alert,
    timestamp: alert.timestamp || new Date(),
    read: false
  }));
}