import { format } from "date-fns";
import { Calendar, Clock, DollarSign } from "lucide-react";
import Link from "next/link";
import type { JobStatus, JobType } from "@/db/schema";
import { useSession } from "@/lib/auth-client";
import { getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Skeleton } from "../ui/skeleton";

// Generic job type that works with both Job and NestedUserJob
type JobListItem = {
  id: string;
  name: string;
  budget: number;
  status: JobStatus;
  startsAt: Date;
  deadline: Date;
  jobType: JobType;
  property?: {
    name: string;
  } | null;
};

interface JobsListProps {
  jobs: JobListItem[];
  isLoading: boolean;
  icon: React.ReactNode;
  actionLabel: string;
  actionHref: (id: string) => string;
}

export function JobsList({
  jobs,
  isLoading,
  icon,
  actionLabel,
  actionHref,
}: JobsListProps) {
  const { data: session } = useSession();
  const isProfessional = session?.user?.role === "contractor";

  if (isLoading) {
    return (
      <div className="divide-y">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4">
            <div className="space-y-3">
              <Skeleton className="h-2 w-1/3" />
              <div className="flex gap-2">
                <Skeleton className="h-1 w-24" />
                <Skeleton className="h-1 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="mb-2 text-muted-foreground">No jobs found</div>
      </div>
    );
  }

  return (
    <div className="divide-y">
      {jobs.map((job) => (
        <div
          key={job.id}
          className="p-3 transition-colors hover:bg-muted/50 sm:p-4"
        >
          <div className="flex flex-col gap-3 sm:flex-row sm:items-start sm:justify-between">
            <div className="min-w-0 flex-1">
              <div className="flex flex-wrap items-center gap-2">
                {icon}
                <Link
                  href={`/projects/${job.id}`}
                  className="truncate font-medium text-base transition-colors hover:text-orange-600 sm:text-lg"
                >
                  {job.name}
                </Link>
                {isProfessional && (
                  <Badge className="bg-blue-100 text-blue-800 text-xs">
                    {job.property?.name || "Property"}
                  </Badge>
                )}
                {!isProfessional && (
                  <Badge
                    variant={getStatusVariant(job.status, JOB_STATUS_VARIANTS)}
                    className="text-xs"
                  >
                    {job.status}
                  </Badge>
                )}
                {job.jobType === "QUICK_HIRE" && (
                  <Badge variant="secondary" className="text-xs">
                    Quick Hire
                  </Badge>
                )}
              </div>
              <div className="mt-2 flex flex-col gap-1 text-muted-foreground text-sm sm:flex-row sm:flex-wrap sm:items-center sm:gap-x-4 sm:gap-y-1">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>Starts: {format(job.startsAt, "MMM d, yyyy")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>Deadline: {format(job.deadline, "MMM d, yyyy")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSign className="h-3.5 w-3.5 flex-shrink-0" />
                  <span>Budget: ${job.budget}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2 sm:flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                className="min-h-[44px] w-full sm:w-auto"
                asChild
              >
                <Link href={actionHref(job.id)}>{actionLabel}</Link>
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
