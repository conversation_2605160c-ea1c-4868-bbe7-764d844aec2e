"use client";

import { useQuery } from "@tanstack/react-query";
import {
  Building,
  Calendar,
  Clock,
  DollarSign,
  Filter,
  Search,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useOrganization } from "@/components/contexts/organization-context";
import { QueryRenderer } from "@/components/query-renderer";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { JobsList } from "../job/jobs-list";

export function ContractorDashboard() {
  const trpc = useTRPC();
  const { organization, isLoading: isLoadingOrganization } = useOrganization();
  const [searchQuery, setSearchQuery] = useState("");
  const [maxDistance, setMaxDistance] = useState(50); // Default 50 miles
  const [activeTab, setActiveTab] = useState("newest");
  const organizationId = organization?.id;

  // Use the PostGIS-powered query
  const { data: allJobs, isLoading: isLoadingAllJobs } = useQuery(
    trpc.jobs.listPublishedByDistance.queryOptions(
      {
        maxDistance,
      },
      {
        enabled: !!organizationId,
      },
    ),
  );

  const { data: myBids, isLoading: isLoadingMyBids } = useQuery(
    trpc.bids.listForOrganization.queryOptions(undefined, {
      enabled: !!organizationId,
    }),
  );

  const { data: activeJobs, isLoading: isLoadingActiveJobs } = useQuery(
    trpc.jobs.listActiveForOrganization.queryOptions(
      { organizationId: organizationId || "" },
      { enabled: !!organizationId },
    ),
  );

  // Filter jobs based on search query
  const filteredJobs = allJobs?.filter((job) =>
    job.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Group jobs by status
  const jobsByCategory = {
    newest: filteredJobs || [],
    bids: myBids?.map((bid) => bid.job) || [],
    active: activeJobs || [],
  };

  // Show loading state if organization is still loading
  if (isLoadingOrganization) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="font-semibold text-xl">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-orange-500" />
                  Loading Dashboard...
                </div>
              </CardTitle>
              <CardDescription>
                Please wait while we load your organization data
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {[...Array(3)].map((_, i) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
              <Skeleton key={i} className="h-[200px]" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle className="font-semibold text-lg sm:text-xl">
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-orange-500" />
                <span className="truncate">
                  {organization?.name || "Organization"} Dashboard
                </span>
              </div>
            </CardTitle>
            <CardDescription className="text-sm">
              Find new jobs and manage your current projects
            </CardDescription>
          </div>
        </div>
        <div className="mt-4 flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-2">
          <div className="relative flex-1">
            <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search jobs..."
              className="min-h-[44px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
            <div className="flex w-full flex-col sm:w-40">
              <span className="mb-1 text-muted-foreground text-xs">
                Distance: {maxDistance} miles
              </span>
              <Slider
                value={[maxDistance]}
                min={5}
                max={100}
                step={5}
                onValueChange={(value) => setMaxDistance(value[0] as number)}
                className="flex min-h-[44px] items-center"
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              className="min-h-[44px] w-full min-w-[44px] sm:w-auto"
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Mobile: Dropdown */}
          <div className="px-6 sm:hidden">
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className="min-h-[44px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <option value="newest">
                Newest Jobs ({jobsByCategory.newest.length})
              </option>
              <option value="bids">
                Our Bids ({jobsByCategory.bids.length})
              </option>
              <option value="active">
                Active Jobs ({jobsByCategory.active.length})
              </option>
            </select>
          </div>

          {/* Desktop: Tabs */}
          <div className="hidden px-6 sm:block">
            <TabsList className="h-auto w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="newest"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Newest Jobs
                <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-200">
                  {jobsByCategory.newest.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="bids"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Our Bids
                <Badge className="ml-2 bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                  {jobsByCategory.bids.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="active"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Active Jobs
                <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
                  {jobsByCategory.active.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="newest" className="m-0">
            <QueryRenderer
              data={allJobs}
              isLoading={isLoadingAllJobs}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No jobs found.</p>
                </div>
              }
            >
              {() => (
                <JobsList
                  jobs={jobsByCategory.newest}
                  isLoading={isLoadingAllJobs}
                  icon={<Calendar className="h-3.5 w-3.5" />}
                  actionLabel="View Details"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>

          <TabsContent value="bids" className="m-0">
            <QueryRenderer
              data={myBids}
              isLoading={isLoadingMyBids}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No bids found.</p>
                </div>
              }
            >
              {() => (
                <JobsList
                  jobs={jobsByCategory.bids}
                  isLoading={isLoadingMyBids}
                  icon={<DollarSign className="h-3.5 w-3.5" />}
                  actionLabel="View Bid"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>

          <TabsContent value="active" className="m-0">
            <QueryRenderer
              data={activeJobs}
              isLoading={isLoadingActiveJobs}
              loadingComponent={
                <div className="grid gap-4">
                  {[...Array(3)].map((_, i) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                    <Skeleton key={i} className="h-[200px]" />
                  ))}
                </div>
              }
              emptyComponent={
                <div className="py-10 text-center">
                  <p className="text-muted-foreground">No active jobs found.</p>
                </div>
              }
            >
              {() => (
                <JobsList
                  jobs={jobsByCategory.active}
                  isLoading={isLoadingActiveJobs}
                  icon={<Clock className="h-3.5 w-3.5" />}
                  actionLabel="Manage Job"
                  actionHref={(id) => `/projects/${id}`}
                />
              )}
            </QueryRenderer>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t px-6 py-4">
        <div className="text-muted-foreground text-xs">
          Showing {filteredJobs?.length || 0} available jobs
        </div>
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/projects/browse">Browse All Jobs</Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
