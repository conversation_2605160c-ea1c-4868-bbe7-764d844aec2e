"use client";

import {
  Building2,
  <PERSON><PERSON>ronUp,
  Crown,
  LogOut,
  Shield,
  User,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import logo from "@/assets/images/tc-logomark.webp";
import { useOrganization } from "@/components/contexts/organization-context";
import { useSidebarNotifications } from "@/hooks/use-sidebar-notifications";
import { signOut, useSession } from "@/lib/auth-client";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Badge } from "./ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "./ui/sidebar";

export function AppSidebar({
  menu,
}: Readonly<{
  menu: {
    title: string;
    href: string;
    icon: React.ElementType;
    visible: boolean;
  }[];
}>) {
  const { data: session } = useSession();
  const { organization } = useOrganization();
  const router = useRouter();
  const pathname = usePathname();
  const isAdmin = session?.user?.role === "admin";
  const isHomeowner = session?.user?.role === "homeowner";
  const isContractor = session?.user?.role === "contractor";

  // Get user initials for avatar fallback
  const userInitials =
    session?.user?.name
      ?.split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase() || "TC";

  // Role badge configuration
  const getRoleBadge = () => {
    if (isAdmin)
      return { label: "Admin", variant: "destructive" as const, icon: Shield };
    if (isContractor)
      return { label: "Pro", variant: "default" as const, icon: Building2 };
    return { label: "Owner", variant: "secondary" as const, icon: Crown };
  };

  const roleBadge = getRoleBadge();
  const notifications = useSidebarNotifications();

  // Get notification count for specific menu items
  const getNotificationCount = (href: string) => {
    switch (href) {
      case "/projects":
        return isContractor
          ? notifications.urgentDeadlines
          : notifications.pendingBids;
      case "/calendar":
        return isContractor ? notifications.urgentDeadlines : 0;
      default:
        return 0;
    }
  };

  return (
    <Sidebar variant="inset" collapsible="offcanvas">
      <SidebarHeader className="border-border/40 border-b">
        <div className="flex items-center gap-3 px-2 py-3">
          <div className="relative">
            <Image
              alt="TradeCrews"
              src={logo}
              height={40}
              width={40}
              className="rounded-lg shadow-sm ring-1 ring-border/20"
            />
            <div className="-bottom-1 -right-1 absolute h-3 w-3 rounded-full bg-green-500 ring-2 ring-background" />
          </div>
          <div className="flex min-w-0 flex-col">
            <div className="font-semibold text-foreground text-sm">
              TradeCrews
            </div>
            {!isHomeowner && organization?.name && (
              <div className="flex items-center gap-1.5">
                <div className="truncate text-muted-foreground text-xs">
                  {organization.name}
                </div>
                <div className="h-1.5 w-1.5 animate-pulse rounded-full bg-green-500" />
              </div>
            )}
            {isHomeowner && (
              <div className="text-muted-foreground text-xs">
                Property Owner
              </div>
            )}
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2 py-4">
        <SidebarMenu className="space-y-1">
          {menu.map(
            (item) =>
              item.visible && (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={cn(
                      "group relative rounded-lg transition-all duration-200 hover:scale-[1.02] hover:bg-accent/50 hover:shadow-sm",
                      pathname === item.href &&
                        "scale-[1.02] bg-accent text-accent-foreground shadow-sm ring-1 ring-border/20",
                    )}
                  >
                    <Link
                      href={item.href}
                      className="flex items-center gap-3 px-3 py-2.5"
                    >
                      <item.icon
                        className={cn(
                          "h-5 w-5 transition-colors",
                          pathname === item.href
                            ? "text-tradecrews-orange-500"
                            : "text-muted-foreground group-hover:text-foreground",
                        )}
                      />
                      <span className="flex-1 font-medium">{item.title}</span>
                      {getNotificationCount(item.href) > 0 && (
                        <Badge
                          variant="destructive"
                          className="h-5 min-w-5 animate-pulse px-1.5 font-semibold text-xs"
                        >
                          {getNotificationCount(item.href)}
                        </Badge>
                      )}
                      {pathname === item.href && (
                        <div className="-translate-y-1/2 absolute top-1/2 left-0 h-6 w-1 rounded-r-full bg-tradecrews-orange-500" />
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ),
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-border/40 border-t p-2">
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="group rounded-lg p-3 transition-all duration-200 hover:bg-accent/50"
                >
                  <div className="flex min-w-0 flex-1 items-center gap-3">
                    <div className="relative">
                      <Avatar className="h-10 w-10 ring-2 ring-border/20">
                        <AvatarImage src={session?.user?.image as string} />
                        <AvatarFallback className="bg-tradecrews-orange-100 font-semibold text-tradecrews-orange-700">
                          {userInitials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="-bottom-0.5 -right-0.5 absolute">
                        <Badge
                          variant={roleBadge.variant}
                          className="h-5 px-1.5 font-medium text-xs"
                        >
                          <roleBadge.icon className="mr-1 h-3 w-3" />
                          {roleBadge.label}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex min-w-0 flex-1 flex-col">
                      <span className="truncate font-semibold text-sm">
                        {session?.user?.name}
                      </span>
                      <span className="truncate text-muted-foreground text-xs">
                        {session?.user?.email}
                      </span>
                    </div>
                  </div>
                  <ChevronUp className="h-4 w-4 text-muted-foreground transition-colors group-hover:text-foreground" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="right"
                className="w-64 p-2"
                align="end"
                sideOffset={8}
              >
                <div className="mb-2 flex items-center gap-3 rounded-lg border border-border/40 p-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={session?.user?.image as string} />
                    <AvatarFallback className="bg-tradecrews-orange-100 text-tradecrews-orange-700 text-xs">
                      {userInitials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex min-w-0 flex-col">
                    <span className="truncate font-medium text-sm">
                      {session?.user?.name}
                    </span>
                    <span className="truncate text-muted-foreground text-xs">
                      {session?.user?.email}
                    </span>
                  </div>
                </div>

                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Profile Settings
                  </Link>
                </DropdownMenuItem>

                {!isAdmin && !isHomeowner && (
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/contractors/${organization?.id}/settings`}
                      className="flex items-center gap-2"
                    >
                      <Building2 className="h-4 w-4" />
                      Company Settings
                    </Link>
                  </DropdownMenuItem>
                )}

                {isAdmin && (
                  <DropdownMenuItem asChild>
                    <Link
                      href="/admin/dashboard"
                      className="flex items-center gap-2"
                    >
                      <Shield className="h-4 w-4" />
                      Admin Dashboard
                    </Link>
                  </DropdownMenuItem>
                )}

                <DropdownMenuSeparator />

                <DropdownMenuItem asChild>
                  <button
                    type="button"
                    className="flex w-full items-center gap-2 text-destructive hover:text-destructive"
                    onClick={() =>
                      signOut({
                        fetchOptions: {
                          onSuccess: () => {
                            router.push("/");
                          },
                        },
                      })
                    }
                  >
                    <LogOut className="h-4 w-4" />
                    Sign Out
                  </button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
