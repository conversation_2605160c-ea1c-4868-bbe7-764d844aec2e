"use client";

import { useState } from "react";
import { useChat } from "ai/react";
import { createIdGenerator } from "ai";
import { <PERSON><PERSON>, Send, Sparkles, Users, BarChart3, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

const ADMIN_QUICK_ACTIONS = [
  {
    icon: Users,
    label: "User Statistics",
    prompt: "Show me comprehensive user statistics for the last 30 days",
    category: "Analytics",
  },
  {
    icon: BarChart3,
    label: "Platform Insights",
    prompt: "Give me platform insights focusing on user engagement trends",
    category: "Analytics",
  },
  {
    icon: Settings,
    label: "Search Users",
    prompt: "Search for users with the role contractor who joined in the last week",
    category: "Management",
  },
  {
    icon: Spark<PERSON>,
    label: "Revenue Analysis",
    prompt: "Analyze revenue trends and provide business intelligence insights",
    category: "Business",
  },
];

interface AdminAIAssistantPanelProps {
  className?: string;
}

export function AdminAIAssistantPanel({ className }: AdminAIAssistantPanelProps) {
  const [chatId, setChatId] = useState<string>("");
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize chat on first use
  const initializeChat = async () => {
    if (!isInitialized) {
      try {
        const response = await fetch("/api/chat/create", {
          method: "POST",
        });
        if (response.ok) {
          const data = await response.json();
          setChatId(data.chatId);
          setIsInitialized(true);
        }
      } catch (error) {
        console.error("Failed to initialize chat:", error);
      }
    }
  };

  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: "/api/chat",
    id: chatId,
    generateId: createIdGenerator({
      prefix: "admin-msg",
      size: 16,
    }),
    sendExtraMessageFields: true,
    experimental_prepareRequestBody: ({ messages }) => {
      const lastMessage = messages[messages.length - 1];
      return {
        message: lastMessage,
        chatId,
      };
    },
    onError: (error) => {
      console.error("Chat error:", error);
    },
  });

  const handleQuickAction = async (prompt: string) => {
    if (!isInitialized) {
      await initializeChat();
    }
    
    // Simulate form submission with the quick action prompt
    const syntheticEvent = {
      preventDefault: () => {},
      target: { elements: { message: { value: prompt } } },
    } as any;
    
    handleInputChange({ target: { value: prompt } } as any);
    setTimeout(() => handleSubmit(syntheticEvent), 100);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-blue-500" />
          Admin AI Assistant
        </CardTitle>
        <CardDescription>
          Get insights, manage users, and analyze platform performance with AI assistance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Actions */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Quick Actions</h4>
          <div className="grid grid-cols-1 gap-2">
            {ADMIN_QUICK_ACTIONS.map((action, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="justify-start h-auto p-3"
                onClick={() => handleQuickAction(action.prompt)}
                disabled={isLoading}
              >
                <action.icon className="h-4 w-4 mr-2 flex-shrink-0" />
                <div className="flex-1 text-left">
                  <div className="font-medium">{action.label}</div>
                  <div className="text-xs text-muted-foreground truncate">
                    {action.prompt}
                  </div>
                </div>
                <Badge variant="secondary" className="ml-2 text-xs">
                  {action.category}
                </Badge>
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Chat Interface */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Chat with Jack</h4>
          
          {/* Messages */}
          <ScrollArea className="h-64 w-full border rounded-md p-3">
            {messages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground text-sm">
                <div className="text-center">
                  <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Hi! I'm Jack, your AI assistant.</p>
                  <p>Ask me about platform analytics, user management, or use the quick actions above.</p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                        Jack is thinking...
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>

          {/* Input */}
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask about platform stats, user management, or analytics..."
              disabled={isLoading}
              onFocus={initializeChat}
            />
            <Button type="submit" size="icon" disabled={isLoading || !input.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>

        {/* Usage Tips */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Try asking:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>"Show me user growth trends"</li>
            <li>"Find users who haven't completed onboarding"</li>
            <li>"Analyze job completion rates by trade"</li>
            <li>"Give me insights on contractor performance"</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
