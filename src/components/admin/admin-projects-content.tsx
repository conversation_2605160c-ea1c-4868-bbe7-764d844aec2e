"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import Link from "next/link";
import { useTRPC } from "@/components/trpc/client";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function AdminProjectsContent() {
  const trpc = useTRPC();
  const { data: projects } = useQuery(trpc.admin.listJobs.queryOptions());

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Property</TableHead>
            <TableHead>Budget</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Bids</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects?.map((project) => (
            <TableRow key={project.id} className="border-b">
              <TableCell className="font-medium">
                <Link
                  href={`/admin/projects/${project.id}`}
                  className="hover:text-orange-600"
                >
                  {project.name}
                </Link>
              </TableCell>
              <TableCell>{project.property?.name || "N/A"}</TableCell>
              <TableCell>${project.budget}</TableCell>
              <TableCell>{project.status}</TableCell>
              <TableCell>{project.bidsCount}</TableCell>
              <TableCell>{format(project.createdAt, "MMM d, yyyy")}</TableCell>
              <TableCell className="text-right">
                <Link
                  href={`/admin/projects/${project.id}`}
                  className={buttonVariants({
                    variant: "outline",
                    size: "sm",
                    className: "mr-2",
                  })}
                >
                  View
                </Link>
                <Link
                  href={`/admin/projects/${project.id}/edit`}
                  className={buttonVariants({
                    variant: "outline",
                    size: "sm",
                  })}
                >
                  Edit
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
