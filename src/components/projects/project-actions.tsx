"use client";

import { useMutation } from "@tanstack/react-query";
import { Pencil, Send } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import type { Job } from "@/db/schema";
import { Button } from "../ui/button";

interface ProjectActionsProps {
  job: Job;
  userRole: string;
  layout?: "horizontal" | "vertical" | "mobile" | "actionbar";
}

export function ProjectActions({
  job,
  userRole,
  layout = "mobile",
}: ProjectActionsProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [isPublishing, setIsPublishing] = useState(false);

  const publishJob = useMutation(
    trpc.jobs.publish.mutationOptions({
      onSuccess: () => {
        toast.success("Project published successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error publishing project: ${error.message}`);
      },
      onSettled: () => {
        setIsPublishing(false);
      },
    }),
  );

  const handlePublish = () => {
    setIsPublishing(true);
    publishJob.mutate({ id: job.id });
  };

  // Define action buttons based on user role and job status
  const isHomeowner = userRole === "homeowner";
  const isProfessional = userRole === "contractor";
  const isDraft = job.status === "DRAFT";
  const isPublished = job.status === "PUBLISHED";
  const isAwarded = job.status === "AWARDED";
  const isCompleted = job.status === "COMPLETED";
  const isQuickHire = job.jobType === "QUICK_HIRE";

  // Mobile-first responsive classes optimized for sticky footer
  const containerClass =
    layout === "vertical"
      ? "space-y-3"
      : layout === "mobile"
        ? "flex gap-2"
        : layout === "actionbar"
          ? "contents" // Use contents to allow children to participate in parent flex
          : "flex gap-2";

  const buttonClass =
    layout === "vertical"
      ? "w-full min-h-[48px]"
      : layout === "mobile"
        ? "flex-1 min-h-[48px] flex-col gap-1 px-2"
        : layout === "actionbar"
          ? "flex flex-col items-center gap-1 px-2 py-1 min-h-[48px] min-w-[48px]"
          : "min-h-[40px]";

  // For mobile and actionbar, use ghost variant and show icons
  const getButtonVariant = (
    originalVariant: "default" | "tc_blue" | "tc_orange",
  ) => {
    return layout === "mobile" || layout === "actionbar"
      ? "ghost"
      : originalVariant;
  };

  return (
    <div className={containerClass}>
      {/* Publish button for draft projects (homeowners only) */}
      {!isProfessional && isDraft && (
        <Button
          variant={getButtonVariant("default")}
          onClick={handlePublish}
          disabled={isPublishing}
          className={`${buttonClass} ${layout !== "mobile" ? "bg-green-600 hover:bg-green-700" : ""}`}
        >
          {layout === "mobile" || layout === "actionbar" ? (
            <>
              <Send className="h-5 w-5" />
              <span className="text-xs">Publish</span>
            </>
          ) : isPublishing ? (
            "Publishing..."
          ) : (
            "Publish Project"
          )}
        </Button>
      )}

      {/* Bid button for professionals on published projects */}
      {isProfessional && isPublished && (
        <Button
          variant={getButtonVariant("tc_blue")}
          asChild
          className={buttonClass}
        >
          <Link href={`/projects/${job.id}/bid`}>
            {layout === "mobile" || layout === "actionbar" ? (
              <>
                <Send className="h-5 w-5" />
                <span className="text-xs">Bid</span>
              </>
            ) : (
              "Bid Now"
            )}
          </Link>
        </Button>
      )}

      {/* Edit actions for homeowners on editable projects */}
      {!isProfessional && !(isAwarded || isCompleted) && (
        <Button
          variant={getButtonVariant("tc_orange")}
          asChild
          className={buttonClass}
        >
          <Link href={`/projects/${job.id}/edit`}>
            {layout === "mobile" || layout === "actionbar" ? (
              <>
                <Pencil className="h-5 w-5" />
                <span className="text-xs">Edit</span>
              </>
            ) : (
              "Edit Project"
            )}
          </Link>
        </Button>
      )}

      {/* Completion actions for awarded projects */}
      {isHomeowner && isAwarded && !isQuickHire && (
        <Button
          variant={getButtonVariant("default")}
          className={`${buttonClass} ${layout !== "mobile" ? "bg-green-600 hover:bg-green-700" : ""}`}
          asChild
        >
          <Link href={`/projects/${job.id}/complete`}>
            {layout === "mobile" || layout === "actionbar" ? (
              <>
                <Send className="h-5 w-5" />
                <span className="text-xs">Complete</span>
              </>
            ) : job.homeownerCompleted ? (
              "Completion Pending"
            ) : (
              "Mark Complete"
            )}
          </Link>
        </Button>
      )}

      {isHomeowner && isAwarded && isQuickHire && (
        <Button
          variant={getButtonVariant("default")}
          asChild
          className={buttonClass}
        >
          <Link href={`/projects/${job.id}/schedule`}>
            {layout === "mobile" || layout === "actionbar" ? (
              <>
                <Send className="h-5 w-5" />
                <span className="text-xs">Schedule</span>
              </>
            ) : (
              "Schedule Job"
            )}
          </Link>
        </Button>
      )}

      {isProfessional && isAwarded && !isQuickHire && (
        <Button
          variant="default"
          className={`${buttonClass} bg-green-600 hover:bg-green-700`}
          asChild
        >
          <Link href={`/projects/${job.id}/complete`}>
            {job.contractorCompleted ? "Completion Pending" : "Mark Complete"}
          </Link>
        </Button>
      )}

      {isProfessional && isAwarded && isQuickHire && (
        <Button variant="default" asChild className={buttonClass}>
          <Link href={`/projects/${job.id}/schedule`}>Schedule Job</Link>
        </Button>
      )}

      {/* Review actions for completed projects */}
      {isCompleted && (
        <Button variant="default" asChild className={buttonClass}>
          <Link href={`/projects/${job.id}/review`}>Leave Review</Link>
        </Button>
      )}
    </div>
  );
}
