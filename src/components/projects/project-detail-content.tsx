"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  CalendarIcon,
  ClockIcon,
  DollarSignIcon,
  MapPinIcon,
  MessageSquareIcon,
  UsersIcon,
} from "lucide-react";
import Image from "next/image";
import { BidCard } from "@/components/bid/bid-card";
import { PusherChat } from "@/components/chat";
import { CrewList } from "@/components/contractor/crew-list";
import { ImageViewerDialog } from "@/components/job/image-viewer-dialog";
import { HomeownerBidFlowTour } from "@/components/tours/homeowner-bid-flow-tour";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import type { Bid, Job } from "@/db/schema";
import { getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";

interface ProjectDetailContentProps {
  jobId: string;
  userId: string;
  userRole: string;
}

export function ProjectDetailContent({
  jobId,
  userId,
  userRole,
}: ProjectDetailContentProps) {
  const trpc = useTRPC();

  const { data: job } = useQuery(
    trpc.jobs.getById.queryOptions({
      id: jobId,
    }),
  );

  if (!job) {
    return <div>Loading...</div>;
  }

  // Type assertion with proper Job type
  const typedJob = job as Job;

  const isHomeowner = userRole === "homeowner";
  const isQuickHire = typedJob.jobType === "QUICK_HIRE";
  const acceptedBid = typedJob.bids?.find(
    (bid: Bid) => bid.status === "ACCEPTED",
  );

  return (
    <div className="space-y-4 sm:space-y-6">
      {isHomeowner &&
        typedJob.status === "PUBLISHED" &&
        typedJob.bids &&
        typedJob.bids.length > 0 && <HomeownerBidFlowTour />}

      {/* Responsive Layout: Single column on mobile, sidebar on desktop */}
      <div className="grid gap-4 sm:gap-6 lg:grid-cols-3">
        {/* Main content - takes 2/3 on desktop */}
        <div className="space-y-4 sm:space-y-6 lg:col-span-2">
          {/* Project Status Header - Mobile First */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="font-semibold text-lg sm:text-xl">
                  Project Status
                </h2>
                <Badge
                  variant={getStatusVariant(
                    typedJob.status,
                    JOB_STATUS_VARIANTS,
                  )}
                >
                  {typedJob.status}
                </Badge>
              </div>

              {/* Property Info - Mobile Optimized */}
              <div className="space-y-3">
                <div>
                  <h3 className="font-medium text-muted-foreground text-sm uppercase tracking-wide">
                    Property
                  </h3>
                  <p className="font-medium">{typedJob.property?.name}</p>
                  {typedJob.property?.address && (
                    <div className="mt-1 flex items-start gap-2">
                      <MapPinIcon className="mt-0.5 size-4 flex-shrink-0 text-tradecrews-orange" />
                      <p className="text-muted-foreground text-sm">
                        {typedJob.property.address.street},{" "}
                        {typedJob.property.address.city},{" "}
                        {typedJob.property.address.state}{" "}
                        {typedJob.property.address.zip}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Details - Mobile First Grid */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-tradecrews-orange/10 p-2">
                    <CalendarIcon className="h-4 w-4 text-tradecrews-orange" />
                  </div>
                  <div>
                    <p className="text-muted-foreground text-xs uppercase tracking-wide">
                      Start Date
                    </p>
                    <p className="font-medium text-sm">
                      {format(typedJob.startsAt, "MMM d, yyyy")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-tradecrews-orange/10 p-2">
                    <ClockIcon className="h-4 w-4 text-tradecrews-orange" />
                  </div>
                  <div>
                    <p className="text-muted-foreground text-xs uppercase tracking-wide">
                      Deadline
                    </p>
                    <p className="font-medium text-sm">
                      {format(typedJob.deadline, "MMM d, yyyy")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-tradecrews-orange/10 p-2">
                    <DollarSignIcon className="h-4 w-4 text-tradecrews-orange" />
                  </div>
                  <div>
                    <p className="text-muted-foreground text-xs uppercase tracking-wide">
                      Budget
                    </p>
                    <p className="font-medium text-sm">${typedJob.budget}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Project Images - Mobile Optimized */}
          {typedJob.images && typedJob.images.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Project Images</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-4">
                  {typedJob.images.map((image, index: number) => (
                    <div
                      key={image.id}
                      className="overflow-hidden rounded-lg border"
                    >
                      <ImageViewerDialog
                        images={
                          typedJob.images?.map((img) => ({
                            url: img.url,
                            description: img.description,
                          })) || []
                        }
                        initialIndex={index}
                        title={`${typedJob.name} - Project Images`}
                      >
                        <div className="relative aspect-square cursor-pointer">
                          <Image
                            src={image.url}
                            alt={
                              image.description || `Project image ${index + 1}`
                            }
                            fill
                            className="object-cover"
                          />
                        </div>
                      </ImageViewerDialog>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tasks - Mobile First */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Tasks</CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              {typedJob.tasks?.length === 0 ? (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">
                    No tasks added to this project.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {typedJob.tasks?.map((task, index: number) => (
                    <div key={task.id} className="rounded-lg border p-4">
                      <h4 className="font-medium">
                        Task {index + 1}: {task.name}
                      </h4>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Bids/Messages (1/3 width on desktop) */}
        <div className="space-y-4 sm:space-y-6">
          {/* Bids/Messages Section */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                {isQuickHire ? (
                  <>
                    <MessageSquareIcon className="h-5 w-5 text-tradecrews-orange" />
                    <CardTitle className="text-lg">Messages</CardTitle>
                  </>
                ) : (
                  <>
                    <UsersIcon className="h-5 w-5 text-tradecrews-orange" />
                    <CardTitle className="text-lg">
                      Bids{" "}
                      {typedJob.bids?.length ? `(${typedJob.bids.length})` : ""}
                    </CardTitle>
                  </>
                )}
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              {isQuickHire ? (
                <PusherChat jobId={typedJob.id} userId={userId} />
              ) : typedJob.bids && typedJob.bids.length > 0 ? (
                <div className="space-y-4">
                  {typedJob.bids.map((bid) => (
                    <BidCard
                      key={bid.id}
                      bidId={bid.id}
                      showAcceptButton={
                        isHomeowner && typedJob.status === "PUBLISHED"
                      }
                    />
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted/50">
                    <UsersIcon className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <p className="text-muted-foreground">
                    No bids have been submitted for this job yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contractor's Crew */}
          {acceptedBid && (
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <UsersIcon className="h-5 w-5 text-tradecrews-orange" />
                  <CardTitle className="text-lg">Contractor's Crew</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <CrewList organizationId={acceptedBid.organizationId} />
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Bottom spacing for mobile actionbar only */}
      <div className="h-20 md:h-0" />
    </div>
  );
}
