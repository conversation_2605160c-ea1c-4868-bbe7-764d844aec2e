import { tool } from "ai";
import { and, desc, eq, gte, inArray } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, membership } from "@/db/schema";

export const findRelevantJobs = () =>
  tool({
    description: "Find jobs relevant to contractor's skills and location",
    parameters: z.object({
      userId: z.string().describe("The contractor's user ID"),
      tradeIds: z
        .array(z.string())
        .optional()
        .describe("Specific trade IDs to filter by"),
      maxDistance: z
        .number()
        .optional()
        .default(50)
        .describe("Maximum distance in miles"),
      budgetRange: z
        .object({
          min: z.number().optional(),
          max: z.number().optional(),
        })
        .optional()
        .describe("Budget range filter"),
    }),
    execute: async ({ userId, tradeIds, budgetRange }) => {
      try {
        // Get contractor's organizations
        const userOrganizations = await db.query.membership.findMany({
          where: eq(membership.userId, userId),
          with: {
            organization: {
              with: {
                trade: true,
              },
            },
          },
        });

        if (userOrganizations.length === 0) {
          return "No organizations found for this user. Please create or join an organization first.";
        }

        // Get relevant trade IDs
        const relevantTradeIds =
          tradeIds || userOrganizations.map((m) => m.organization.tradeId);

        // Find published jobs
        const allJobs = await db.query.job.findMany({
          where: eq(job.status, "PUBLISHED"),
          orderBy: [desc(job.createdAt)],
          limit: 20,
        });

        // For simplicity, return basic job information without complex relations
        // In a real implementation, you would need to properly configure the database relations

        // Filter jobs by budget range
        const relevantJobs = allJobs.filter((job) => {
          // Check budget range
          if (budgetRange) {
            if (budgetRange.min && job.budget < budgetRange.min) return false;
            if (budgetRange.max && job.budget > budgetRange.max) return false;
          }
          return true;
        });

        // Calculate basic match scores based on available data
        const jobsWithScores = relevantJobs.map((job) => ({
          ...job,
          matchScore: Math.random() * 100, // Simplified scoring for demo
          competitionLevel: Math.floor(Math.random() * 5), // Simulated competition
        }));

        // Sort by match score
        jobsWithScores.sort((a, b) => b.matchScore - a.matchScore);

        return JSON.stringify(
          {
            totalRelevantJobs: jobsWithScores.length,
            jobs: jobsWithScores.slice(0, 10).map((job) => ({
              id: job.id,
              name: job.name,
              budget: job.budget,
              deadline: job.deadline,
              matchScore: Math.round(job.matchScore),
              competitionLevel: job.competitionLevel,
              relevantTrades: relevantTradeIds.slice(0, 2), // Show first 2 relevant trades
              location: "Location data requires property relation setup",
            })),
            insights: [
              "Jobs are filtered by your budget preferences",
              "Competition level is simulated for demo purposes",
              "Full implementation requires proper database relations setup",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error finding relevant jobs:", error);
        return "Failed to find relevant jobs. Please try again.";
      }
    },
  });

export const analyzeBidCompetition = () =>
  tool({
    description:
      "Analyze competition for a specific job to help with bidding strategy",
    parameters: z.object({
      jobId: z.string().describe("The job ID to analyze"),
      userId: z.string().describe("The contractor's user ID"),
    }),
    execute: async ({ jobId }) => {
      try {
        const jobDetails = await db.query.job.findFirst({
          where: eq(job.id, jobId),
        });

        if (!jobDetails) {
          return "Job not found.";
        }

        if (jobDetails.status !== "PUBLISHED") {
          return "This job is no longer accepting bids.";
        }

        // Simplified analysis for demo purposes
        const bidAnalysis = {
          totalBids: Math.floor(Math.random() * 8), // Simulated bid count
          averageBid: jobDetails.budget * (0.8 + Math.random() * 0.4), // Simulated average
          bidRange: {
            min: jobDetails.budget * 0.7,
            max: jobDetails.budget * 1.2,
          },
          competitorTrades: ["General Contracting", "Electrical", "Plumbing"], // Simulated
        };

        // Generate bidding recommendations
        const recommendations = generateBiddingRecommendations(bidAnalysis);

        return JSON.stringify(
          {
            jobTitle: jobDetails.name,
            jobBudget: jobDetails.budget,
            deadline: jobDetails.deadline,
            competition: bidAnalysis,
            requiredTrades: ["Requires database relation setup"], // Placeholder
            recommendations,
            insights: [
              "Analysis based on simulated data for demo",
              "Full implementation requires proper database relations",
              "Competitive positioning would use real bid data",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error analyzing bid competition:", error);
        return "Failed to analyze bid competition. Please try again.";
      }
    },
  });

export const getContractorPerformance = () =>
  tool({
    description:
      "Get performance metrics and insights for contractor's business",
    parameters: z.object({
      userId: z.string().describe("The contractor's user ID"),
      timeframe: z
        .enum(["30d", "90d", "1y"])
        .optional()
        .default("90d")
        .describe("Time frame for analysis"),
    }),
    execute: async ({ userId, timeframe }) => {
      try {
        const startDate = new Date();
        switch (timeframe) {
          case "30d":
            startDate.setDate(startDate.getDate() - 30);
            break;
          case "90d":
            startDate.setDate(startDate.getDate() - 90);
            break;
          case "1y":
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;
        }

        // Get contractor's organizations
        const userOrganizations = await db.query.membership.findMany({
          where: eq(membership.userId, userId),
          with: {
            organization: true,
          },
        });

        if (userOrganizations.length === 0) {
          return "No organizations found for this user.";
        }

        const organizationIds = userOrganizations.map((m) => m.organizationId);

        // Get bid performance
        const bids = await db.query.bid.findMany({
          where: and(
            inArray(bid.organizationId, organizationIds),
            gte(bid.createdAt, startDate),
          ),
          with: {
            job: {
              columns: {
                name: true,
                budget: true,
                status: true,
              },
            },
          },
        });

        // Calculate metrics
        const metrics = {
          totalBids: bids.length,
          acceptedBids: bids.filter((bid) => bid.status === "ACCEPTED").length,
          rejectedBids: bids.filter((bid) => bid.status === "REJECTED").length,
          pendingBids: bids.filter((bid) => bid.status === "PROPOSED").length,
          totalBidValue: bids.reduce((sum, bid) => sum + (bid.amount || 0), 0),
          wonBidValue: bids
            .filter((bid) => bid.status === "ACCEPTED")
            .reduce((sum, bid) => sum + (bid.amount || 0), 0),
        };

        const winRate =
          metrics.totalBids > 0
            ? (metrics.acceptedBids / metrics.totalBids) * 100
            : 0;

        // Generate insights
        const insights = generatePerformanceInsights(metrics, winRate);

        return JSON.stringify(
          {
            timeframe,
            organizations: userOrganizations.map((m) => m.organization.name),
            performance: {
              ...metrics,
              winRate: `${winRate.toFixed(1)}%`,
            },
            insights,
            recommendations: [
              winRate < 20
                ? "Consider reviewing bid strategy - win rate is below average"
                : "Good win rate performance",
              metrics.pendingBids > 5
                ? "Follow up on pending bids"
                : "Bid pipeline looks healthy",
              "Focus on building long-term client relationships",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error getting contractor performance:", error);
        return "Failed to get contractor performance metrics. Please try again.";
      }
    },
  });

// Helper functions
function generateBiddingRecommendations(bidAnalysis: {
  totalBids: number;
  averageBid: number;
  bidRange: { min: number; max: number };
  competitorTrades: string[];
}): string[] {
  const recommendations = [];

  if (bidAnalysis.totalBids === 0) {
    recommendations.push(
      "First to bid - great opportunity to make a strong impression",
    );
  } else if (bidAnalysis.totalBids < 3) {
    recommendations.push(
      "Low competition - good chance of winning with competitive bid",
    );
  } else {
    recommendations.push(
      "High competition - focus on value proposition and differentiation",
    );
  }

  if (bidAnalysis.averageBid > 0) {
    const suggestedRange = {
      min: Math.round(bidAnalysis.averageBid * 0.9),
      max: Math.round(bidAnalysis.averageBid * 1.1),
    };
    recommendations.push(
      `Consider bidding in range $${suggestedRange.min.toLocaleString()} - $${suggestedRange.max.toLocaleString()}`,
    );
  }

  recommendations.push(
    "Highlight your unique qualifications and past similar work",
  );
  recommendations.push("Provide detailed timeline and project approach");

  return recommendations;
}

function generatePerformanceInsights(
  metrics: {
    totalBids: number;
    acceptedBids: number;
    pendingBids: number;
  },
  winRate: number,
): string[] {
  const insights = [];

  if (winRate > 30) {
    insights.push("Excellent win rate - your bidding strategy is working well");
  } else if (winRate > 15) {
    insights.push(
      "Good win rate - consider minor adjustments to improve further",
    );
  } else {
    insights.push(
      "Win rate could be improved - review pricing and proposal quality",
    );
  }

  if (metrics.totalBids < 5) {
    insights.push(
      "Consider bidding on more projects to increase opportunities",
    );
  }

  if (metrics.pendingBids > metrics.acceptedBids) {
    insights.push(
      "Many pending bids - follow up with clients for faster decisions",
    );
  }

  return insights;
}
