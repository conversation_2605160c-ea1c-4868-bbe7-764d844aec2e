import { tool } from "ai";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { job, property } from "@/db/schema";

export const getProjectRecommendations = () =>
  tool({
    description:
      "Get personalized project recommendations and insights for homeowners",
    parameters: z.object({
      userId: z.string().describe("The user ID to get recommendations for"),
      projectType: z
        .string()
        .optional()
        .describe("Specific type of project to focus on"),
      budgetRange: z
        .object({
          min: z.number().optional(),
          max: z.number().optional(),
        })
        .optional()
        .describe("Budget range for recommendations"),
    }),
    execute: async ({ userId }) => {
      try {
        // Get user's properties
        const userProperties = await db.query.property.findMany({
          where: eq(property.userId, userId),
          columns: {
            id: true,
            name: true,
            addressId: true,
            userId: true,
          },
        });

        // For demo purposes, we'll use simplified data
        // In a full implementation, you would query jobs through property relationship

        // Generate recommendations based on property characteristics
        const recommendations = generateProjectRecommendations();

        return JSON.stringify(
          {
            userProperties: userProperties.length,
            recommendations,
            insights: [
              "Based on your property characteristics",
              "Seasonal project timing considerations",
              "General maintenance recommendations",
              "Note: Full implementation requires database relation setup",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error getting project recommendations:", error);
        return "Failed to generate project recommendations. Please try again.";
      }
    },
  });

export const analyzeProjectBids = () =>
  tool({
    description:
      "Analyze bids for a specific project and provide recommendations",
    parameters: z.object({
      jobId: z.string().describe("The job ID to analyze bids for"),
      userId: z.string().describe("The user ID (must be the job owner)"),
    }),
    execute: async ({ jobId, userId }) => {
      try {
        // Get job details first
        const jobDetails = await db.query.job.findFirst({
          where: eq(job.id, jobId),
        });

        if (!jobDetails) {
          return "Job not found.";
        }

        // Verify user owns the job through property relationship
        const jobProperty = await db.query.property.findFirst({
          where: eq(property.id, jobDetails.propertyId),
          columns: {
            userId: true,
          },
        });

        if (!jobProperty || jobProperty.userId !== userId) {
          return "You don't have permission to view this job.";
        }

        // For demo purposes, return simplified analysis
        // In a full implementation, you would query bids and analyze them
        const bidAnalysis = {
          totalBids: Math.floor(Math.random() * 5) + 1,
          averageBid: jobDetails.budget * (0.8 + Math.random() * 0.4),
          lowestBid: jobDetails.budget * 0.7,
          highestBid: jobDetails.budget * 1.2,
        };

        // Generate recommendations
        const recommendations = [
          "Compare bids based on value, not just price",
          "Check contractor references and past work",
          "Verify licensing and insurance",
          "Consider timeline and availability",
        ];

        return JSON.stringify(
          {
            jobTitle: jobDetails.name,
            totalBids: bidAnalysis.totalBids,
            budgetComparison: bidAnalysis,
            recommendations,
            insights: [
              "Analysis based on simulated data for demo",
              "Full implementation requires proper database relations",
              "Bid comparison would use real contractor data",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error analyzing project bids:", error);
        return "Failed to analyze project bids. Please try again.";
      }
    },
  });

export const getProjectTimeline = () =>
  tool({
    description: "Generate a realistic project timeline and milestones",
    parameters: z.object({
      jobId: z.string().describe("The job ID to create timeline for"),
      userId: z.string().describe("The user ID (must be the job owner)"),
    }),
    execute: async ({ jobId, userId }) => {
      try {
        // Get job details first
        const jobDetails = await db.query.job.findFirst({
          where: eq(job.id, jobId),
        });

        if (!jobDetails) {
          return "Job not found.";
        }

        // Verify user owns the job through property relationship
        const jobProperty = await db.query.property.findFirst({
          where: eq(property.id, jobDetails.propertyId),
          columns: {
            userId: true,
          },
        });

        if (!jobProperty || jobProperty.userId !== userId) {
          return "You don't have permission to view this job.";
        }

        // Generate simplified timeline for demo
        const timeline = generateProjectTimeline(jobDetails);

        return JSON.stringify(
          {
            projectName: jobDetails.name,
            estimatedDuration: timeline.totalDuration,
            phases: timeline.phases,
            milestones: timeline.milestones,
            considerations: [
              "Weather and seasonal factors",
              "Permit and inspection requirements",
              "Material delivery schedules",
              "Contractor availability",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error generating project timeline:", error);
        return "Failed to generate project timeline. Please try again.";
      }
    },
  });

// Helper functions
function generateProjectRecommendations() {
  const recommendations = [];

  // General seasonal recommendations
  const currentMonth = new Date().getMonth();
  if (currentMonth >= 2 && currentMonth <= 5) {
    // Spring
    recommendations.push({
      type: "seasonal",
      title: "Exterior Painting",
      reason: "Spring is ideal for exterior painting projects",
      priority: "medium",
      estimatedCost: "$3000-12000",
    });
  }

  // General maintenance recommendations
  recommendations.push({
    type: "maintenance",
    title: "HVAC System Inspection",
    reason: "Regular HVAC maintenance improves efficiency",
    priority: "high",
    estimatedCost: "$200-500",
  });

  recommendations.push({
    type: "improvement",
    title: "Energy Efficiency Upgrade",
    reason: "Improve insulation and windows for energy savings",
    priority: "medium",
    estimatedCost: "$2000-8000",
  });

  return recommendations;
}

function generateProjectTimeline(job: {
  name: string;
  startsAt: Date;
  deadline: Date;
}) {
  const phases = [];
  const startDate = new Date(job.startsAt);
  const endDate = new Date(job.deadline);
  const totalDays = Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
  );

  // Planning phase
  phases.push({
    name: "Planning & Permits",
    duration: "1-2 weeks",
    tasks: ["Finalize design", "Obtain permits", "Order materials"],
  });

  // Execution phase (simplified)
  phases.push({
    name: "Main Construction",
    duration: `${Math.max(1, Math.floor(totalDays * 0.7))} days`,
    tasks: ["Primary work execution"],
  });

  // Final phase
  phases.push({
    name: "Final Inspection & Cleanup",
    duration: "2-3 days",
    tasks: ["Final inspection", "Touch-ups", "Cleanup"],
  });

  return {
    totalDuration: `${Math.ceil(totalDays / 7)} weeks`,
    phases,
    milestones: [
      "Project kickoff",
      "Permits approved",
      "Materials delivered",
      "50% completion",
      "Final inspection",
      "Project completion",
    ],
  };
}
