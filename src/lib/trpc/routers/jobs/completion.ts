import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod/v4";
import { db } from "@/db";
import { bid, job } from "@/db/schema";
import { jobProcedures } from "@/lib/trpc/procedures";
import { getJobWithRelations } from "@/lib/trpc/utils/entities";

export const completionRouter = {
  markComplete: jobProcedures.update
    .input(
      z.object({
        jobId: z.string(),
        role: z
          .string()
          .refine(
            (val) => val === "homeowner" || val === "contractor",
            "Role must be either homeowner or contractor",
          ),
      }),
    )
    .mutation(async ({ input }) => {
      const result = await getJobWithRelations(input.jobId);

      // Find the accepted bid for this job
      const acceptedBid = await db.query.bid.findFirst({
        where: and(eq(bid.jobId, input.jobId), eq(bid.status, "ACCEPTED")),
        with: {
          organization: true,
        },
      });

      if (!acceptedBid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "No accepted bid found for this job",
        });
      }

      // Update the appropriate completion field
      if (input.role === "homeowner") {
        result.homeownerCompleted = true;
      } else {
        result.contractorCompleted = true;
      }

      // If both parties have marked as complete, update the job status
      if (
        (input.role === "homeowner" && job.contractorCompleted) ||
        (input.role === "contractor" && job.homeownerCompleted)
      ) {
        result.status = "COMPLETED";
        result.completedAt = new Date();
      }

      // Update the job
      return await db
        .update(job)
        .set(result)
        .where(eq(job.id, input.jobId))
        .returning();
    }),
};
